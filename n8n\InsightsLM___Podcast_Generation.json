{"name": "InsightsLM - Podcast Generation", "nodes": [{"parameters": {"command": "if ffmpeg -version > /dev/null 2>&1; then\n  echo \"FFmpeg is installed\"\nelse\n  echo \"FFmpeg is NOT installed\"\nfi\n"}, "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [-420, 1320], "id": "b804e4c1-5f49-4fb6-943b-92f183c302a9", "name": "Check is FFMPEG Installed"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "05ef5c0e-fe71-4367-a88d-6ba65b8e4e48", "leftValue": "={{ $json.stdout }}", "rightValue": "=FFmpeg is installed", "operator": {"type": "string", "operation": "equals"}}, {"id": "3e8219ed-4cfb-4706-b6c0-6cdf4e1d1c3b", "leftValue": "", "rightValue": "", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-200, 1320], "id": "3adf9104-7091-4b88-b45b-efdb219c7742", "name": "If"}, {"parameters": {"respondWith": "json", "responseBody": "{\n    \"message\": \"Workflow was started\"\n}", "options": {"responseCode": 200}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.4, "position": [100, 1200], "id": "54c2c7a8-af45-488e-9439-997d43ca2676", "name": "Respond to Webhook"}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"error\": \"FFmpeg not installed\",\n  \"code\": \"FFMPEG_NOT_INSTALLED\",\n  \"details\": \"The server requires ffmpeg to be installed\"\n}", "options": {"responseCode": 500}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.4, "position": [100, 1380], "id": "2b492335-fad2-4981-8c5c-bd790256bddd", "name": "Respond with 500 Error"}, {"parameters": {"content": "## To Do \n- REQUIREMENT: FFMPEG needs to be installed on your server for this workflow to succeed\n- Configure the Supabase nodes to reference your Project\n- Configure your Google Cloud API credentials\n- Update the Host of the \"HTTP Requests\" to hit your project", "width": 680, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [2040, 840], "id": "73fb9911-4a7c-413b-8bb9-9ec86a3f501e", "name": "Sticky Note1"}, {"parameters": {"httpMethod": "POST", "path": "4c4699bc-004b-4ca3-8923-373ddd4a274e", "authentication": "headerAuth", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-680, 1320], "id": "36040655-7e2a-4af2-8524-f0e84fa2c874", "name": "Webhook", "webhookId": "4c4699bc-004b-4ca3-8923-373ddd4a274e", "credentials": {"httpHeaderAuth": {"id": "39evQ95L86jhtb3I", "name": "MyNoteBookLM Auth"}}}, {"parameters": {"operation": "getAll", "tableId": "sources", "returnAll": true, "filters": {"conditions": [{"keyName": "notebook_id", "condition": "eq", "keyValue": "={{ $('Webhook').item.json.body.notebook_id }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [360, 1200], "id": "2bf3e04b-7472-4a31-80e3-c78fb8f742be", "name": "Get Sources", "credentials": {"supabaseApi": {"id": "OeYUddl4OaIohMCC", "name": "MynotebookLM Supabase"}}}, {"parameters": {"aggregate": "aggregateAllItemData", "include": "specifiedFields", "fieldsToInclude": "title,content", "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [580, 1200], "id": "30a71143-107d-4c02-a43f-54d972c42437", "name": "Aggregate"}, {"parameters": {"assignments": {"assignments": [{"id": "c3743cbc-c838-401a-8f65-95a386707ded", "name": "prompt", "value": "=I’ll give you the text of SOURCE(S) and I’d like you to use the instructions below to write a podcast script\n\n# EXAMPLE OUTPUT STRUCTURE & TRANSCRIPT\n\nPlease read aloud the following in a podcast interview style:\nSpeaker 1: When you watch a Formula One car flash by, it's just an incredible sight. \nSpeaker 2: These things are absolute apex predators of engineering, aren't they? Machines designed purely for speed, pushing everything, materials, aero to the absolute limit. Every single part feels like a technological marvel. It really does.\nSpeaker 1: And what's amazing is that these cars, they weren't just dreamt up in a vacuum. You know, they actually exist within this incredibly tight box, a really, really detailed rulebook that dictates pretty much everything that is and isn't possible.\nSpeaker 2: Exactly. And for this deep dive, we're going straight to the source of that box. Our material today is key sections from the official 2025 Formula One technical regulations put out by the sports governing body, the FIA. Yeah. And our mission here for you listening is really to kind of pull back the curtain on that rulebook. We want to unpack these regulations, try and understand the fundamental principles, maybe some of the surprising details, and just grasp how much complexity is actually baked into the blueprint of an F1 car. It's not just, you know, dry rules. It's the framework that shapes performance, safety and that constant, fascinating battle of innovation we see season after season.\nSpeaker 1: OK, so let's start right at the top then. Yeah. The regulations make it pretty clear who's running the show. It's the FIA, right? They own and organize the Formula One World Championship. They're the ones handing out those coveted titles for drivers, constructors.\nSpeaker 2: That's right. And beneath that sort of top level, there are layers upon layers of rules governing absolutely everything. The championship itself, every single competition within it. So each race weekend, they're governed by the International Sporting Code. These technical regulations we're looking at today, the sporting regulations which cover race procedures, penalties, that kind of thing.\n\n# INSTRUCTIONS TO FOLLOW\n\n1. Opening:\n– Begin with some interesting opening remarks on the topic between the hosts\n– Then introduce the topic as a “deep dive” into the subject matter.\n\n2. Dialog Structure:\n– Use two hosts who engage in a conversational back-and-forth.\n– Alternate between short, punchy statements and longer explanations.\n– Use frequent affirmations like “Right,” “Exactly,” and “Absolutely” to maintain flow and agreement.\n\n3. Language and Tone:\n– Keep the language informal and accessible. Use contractions and colloquialisms.\n– Maintain an enthusiastic, energetic tone throughout.\n– Use rhetorical questions to transition between points: “It’s fascinating, isn’t it?”\n– Employ phrases like “You know” and “I mean” to maintain a casual feel.\n\n4. Content Presentation:\n– Introduce source material (e.g., articles, studies) early in the discussion.\n– Use analogies to explain complex concepts: “It’s like…”\n– Break down ideas into digestible chunks, often using numbered points or clear transitions.\n\n5. Interaction Between Hosts:\n– Have one host pose questions or express confusion, allowing the other to explain.\n– Use phrases like “You’ve hit the nail on the head” to validate each other’s points.\n– Build on each other’s ideas, creating a collaborative feel.\n\n6. Engagement Techniques:\n– Address the audience directly at times: “So to everyone listening…”\n– Pose thought-provoking questions for the audience to consider.\n\n7. Structure and Pacing:\n– Start with a broad introduction of the topic and narrow down to specific points.\n– Use phrases like “So we’ve established…” to summarize and move to new points.\n– Maintain a brisk pace, but allow for moments of reflection on bigger ideas.\n\n8. Concluding the Episode:\n– Signal the wrap-up with “So as we wrap things up…”\n– Pose a final thought-provoking question or takeaway.\n– Use the phrase “And on that note…” to transition to the closing.\n– Encourage continued engagement: “stay curious, keep those questions coming.”\n– End with a consistent sign-off: “Until next time, keep [relevant verb].”\n\n9. Overall Flow:\n– Begin with the misconception or general understanding of the topic.\n– Introduce expert sources that challenge or deepen this understanding.\n– Discuss implications and broader context of the new information.\n– Conclude with how this knowledge affects the listener or the field at large.\n\nRemember to maintain a balance between informative content and engaging conversation, always keeping the tone friendly and accessible regardless of the complexity of the topic.\n\n# SOURCE(S) TO CREATE TRANSCRIPT FROM\n\n{{ JSON.stringify($json.data) }}\n\n# OTHER\n\nIMPORTANT: Only output the transcript and use \\n in between speakers dialog\nIMPORTANT: Your response must start with ...\n\"Please read aloud the following in a podcast interview style:\nSpeaker 1: \"", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [840, 1200], "id": "a12a3968-6929-489f-a497-a837f3c7e13c", "name": "Set Prompt"}, {"parameters": {"promptType": "define", "text": "={{ $json.prompt }}", "batching": {}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.7, "position": [1100, 1180], "id": "25ae0627-f92b-4fa3-8722-6ce6cb678697", "name": "Basic LLM Chain", "retryOnFail": true, "waitBetweenTries": 5000, "maxTries": 5}, {"parameters": {"model": {"__rl": true, "value": "gpt-4.1", "mode": "list", "cachedResultName": "gpt-4.1"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [1100, 1400], "id": "a805fc31-c265-49cc-8a95-e79ec7311783", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "hNalDChhNUDtYG7T", "name": "OpenAi account"}}}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-tts:generateContent", "authentication": "predefinedCredentialType", "nodeCredentialType": "googlePalmApi", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"contents\": [{\n    \"parts\":[{\n      \"text\": {{ JSON.stringify($json.text) }}\n    }]\n  }],\n  \"generationConfig\": {\n    \"responseModalities\": [\"AUDIO\"],\n    \"speechConfig\": {\n      \"multiSpeakerVoiceConfig\": {\n        \"speakerVoiceConfigs\": [{\n            \"speaker\": \"<PERSON>\",\n            \"voiceConfig\": {\n              \"prebuiltVoiceConfig\": {\n                \"voiceName\": \"Algenib\"\n              }\n            }\n          }, {\n            \"speaker\": \"<PERSON>\",\n            \"voiceConfig\": {\n              \"prebuiltVoiceConfig\": {\n                \"voiceName\": \"Kore\"\n              }\n            }\n          }]\n      }\n    }\n  },\n  \"model\": \"gemini-2.5-flash-preview-tts\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1520, 1180], "id": "acf52aa8-77ad-41ba-8b27-66f496be6454", "name": "Generate Audio", "retryOnFail": true, "waitBetweenTries": 5000, "maxTries": 5, "credentials": {"googlePalmApi": {"id": "PzC8XiX0nzmyH9AA", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"operation": "toBinary", "sourceProperty": "candidates[0].content.parts[0].inlineData.data", "options": {"fileName": "audio.pcm", "mimeType": "audio/L16"}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [1740, 1180], "id": "0c297369-7ff9-45f8-be19-83191b69a676", "name": "Convert to File"}, {"parameters": {"operation": "write", "fileName": "=/tmp/{{ $('Generate Audio').item.json.responseId }}-in.pcm", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [1960, 1180], "id": "d6a89506-9cd2-4486-85a1-be20060d5d04", "name": "Read/Write Files from Disk"}, {"parameters": {"command": "=ffmpeg -f s16le -ar 24000 -ac 1 -i /tmp/{{ $('Generate Audio').item.json.responseId }}-in.pcm -codec:a libmp3lame -qscale:a 2 /tmp/{{ $('Generate Audio').item.json.responseId }}-out.mp3"}, "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [2200, 1180], "id": "d3f96ec9-2373-4ca7-8282-c078de61b92b", "name": "Execute Command"}, {"parameters": {"fileSelector": "=/tmp/{{ $('Generate Audio').item.json.responseId }}-out.mp3", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [2420, 1180], "id": "5d91d055-300e-4a33-b3e9-fac3b25dd3e0", "name": "Read/Write Files from Disk1"}, {"parameters": {"method": "POST", "url": "=https://yfvmutoxmibqzvyklggr.supabase.co/storage/v1/object/audio/{{ $('Webhook').item.json.body.notebook_id}}/{{ $binary.data.fileName }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "supabaseApi", "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"parameterType": "formBinaryData", "name": "file", "inputDataFieldName": "data"}]}, "options": {"response": {"response": {"fullResponse": true}}}}, "id": "65a6bb6c-99e1-4e17-ac97-95bf0e23a954", "name": "Upload object", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [2640, 1180], "credentials": {"supabaseApi": {"id": "OeYUddl4OaIohMCC", "name": "MynotebookLM Supabase"}}}, {"parameters": {"method": "POST", "url": "=https://yfvmutoxmibqzvyklggr.supabase.co/storage/v1/object/sign/{{ $json.body.Key }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "supabaseApi", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"expiresIn\": 86400\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2840, 1180], "id": "f05a5230-c0c5-4b55-b50c-57b1a3c4df83", "name": "Generate Signed URL", "credentials": {"supabaseApi": {"id": "OeYUddl4OaIohMCC", "name": "MynotebookLM Supabase"}}}, {"parameters": {"jsCode": "// Loop over input items and add a new field called 'myNewField' to the JSON of each one\nfor (const item of $input.all()) {\n  const now = new Date();\n  const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000); // Add 1 day in ms\n  item.json.timestamp = tomorrow.toISOString(); // Compatible with Supabase timestampz\n}\n\nreturn $input.all();\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [3080, 1180], "id": "8d5764b9-498a-4a73-a62a-69ccb6fa5716", "name": "Generate Timestamp for Expires"}, {"parameters": {"operation": "update", "tableId": "notebooks", "filters": {"conditions": [{"keyName": "id", "condition": "eq", "keyValue": "={{ $('Webhook').item.json.body.notebook_id }}"}]}, "fieldsUi": {"fieldValues": [{"fieldId": "audio_overview_url", "fieldValue": "=https://yfvmutoxmibqzvyklggr.supabase.co/storage/v1/{{ $json.signedURL }}"}, {"fieldId": "audio_url_expires_at", "fieldValue": "={{ $json.timestamp }}"}, {"fieldId": "audio_overview_generation_status", "fieldValue": "completed"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [3340, 1180], "id": "e549327c-f98e-4119-82b0-92c8e902fd06", "name": "Supabase", "credentials": {"supabaseApi": {"id": "OeYUddl4OaIohMCC", "name": "MynotebookLM Supabase"}}}, {"parameters": {"content": "[![The AI Automators](https://www.theaiautomators.com/wp-content/uploads/2025/03/gray-logo.png)](https://www.theaiautomators.com/)\n## InsightsLM\nhttps://github.com/theaiautomators/insights-lm-public", "height": 220, "width": 280, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-680, 900], "id": "f1d8ead0-76e7-4a47-8399-969861b08a46", "name": "Sticky Note8"}], "pinData": {}, "connections": {"Check is FFMPEG Installed": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}], [{"node": "Respond with 500 Error", "type": "main", "index": 0}]]}, "Respond to Webhook": {"main": [[{"node": "Get Sources", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Check is FFMPEG Installed", "type": "main", "index": 0}]]}, "Get Sources": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "Set Prompt", "type": "main", "index": 0}]]}, "Set Prompt": {"main": [[{"node": "Basic LLM Chain", "type": "main", "index": 0}]]}, "Basic LLM Chain": {"main": [[{"node": "Generate Audio", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Basic LLM Chain", "type": "ai_languageModel", "index": 0}]]}, "Generate Audio": {"main": [[{"node": "Convert to File", "type": "main", "index": 0}]]}, "Convert to File": {"main": [[{"node": "Read/Write Files from Disk", "type": "main", "index": 0}]]}, "Read/Write Files from Disk": {"main": [[{"node": "Execute Command", "type": "main", "index": 0}]]}, "Execute Command": {"main": [[{"node": "Read/Write Files from Disk1", "type": "main", "index": 0}]]}, "Read/Write Files from Disk1": {"main": [[{"node": "Upload object", "type": "main", "index": 0}]]}, "Upload object": {"main": [[{"node": "Generate Signed URL", "type": "main", "index": 0}]]}, "Generate Signed URL": {"main": [[{"node": "Generate Timestamp for Expires", "type": "main", "index": 0}]]}, "Generate Timestamp for Expires": {"main": [[{"node": "Supabase", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "f228bda4-191c-47b3-a8b0-e8e16d374eb5", "meta": {"templateCredsSetupCompleted": true, "instanceId": "b5d1ea132a4e071e6288b3143f31284b91560858bdef3f0c88a49f587fc91a29"}, "id": "bxrCsRKl157B5Lyt", "tags": [{"createdAt": "2025-05-12T13:43:59.783Z", "updatedAt": "2025-05-12T13:43:59.783Z", "id": "d3ygIhrGjDmzgrW0", "name": "TheAIAutomators.com"}]}