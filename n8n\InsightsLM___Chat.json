{"name": "InsightsLM - Chat", "nodes": [{"parameters": {"httpMethod": "POST", "path": "2fabf43f-6e6e-424b-8e93-9150e9ce7d6c", "authentication": "headerAuth", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-40, -100], "id": "d74f0275-ed31-49eb-af44-2ff2bf95571b", "name": "Webhook", "webhookId": "2fabf43f-6e6e-424b-8e93-9150e9ce7d6c", "credentials": {"httpHeaderAuth": {"id": "39evQ95L86jhtb3I", "name": "MyNoteBookLM Auth"}}}, {"parameters": {"promptType": "define", "text": "={{ $json.body.message }}", "hasOutputParser": true, "options": {"systemMessage": "=You are tasked with answering a question using provided chunks of information. \n\nYour goal is to provide an accurate answer from these chunks while citing your sources. When you use information from a specific chunk in your answer, you must cite it using the specified JSON output format.\n\nThe citation should appear at the end of the sentence or paragraph where the information is used.\n\nTake note of the index of the chunk returned from the vector store, the source_id of the chunk as well as the lines from and lines to... that way we can trace back the cited source.\n\nIf you cannot answer the question using the provided chunks, say \"Sorry I don't know\".\n\nImportant: You MUST trigger the \"Supabase Vector Store\" tool\nImportant: Only based your answers on information in the provided chunks from the vector store"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [220, -180], "id": "c905f3d9-51bc-4997-983d-ce73be6912cc", "name": "AI Agent", "retryOnFail": true, "waitBetweenTries": 5000, "onError": "continueErrorOutput"}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $json.body.session_id }}", "contextWindowLength": 20}, "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1.3, "position": [340, 200], "id": "01be19ed-3504-463f-9552-b975883dbefd", "name": "Postgres Chat Memory", "credentials": {"postgres": {"id": "OuPBM6n0hyobX8h6", "name": "Postgres account"}}}, {"parameters": {"mode": "retrieve-as-tool", "toolName": "retrieve_knowledge", "toolDescription": "Retrieve releveant information", "tableName": {"__rl": true, "value": "documents", "mode": "list", "cachedResultName": "documents"}, "topK": 10, "options": {"metadata": {"metadataValues": [{"name": "notebook_id", "value": "={{ $json.body.session_id }}"}]}}}, "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1.1, "position": [500, 140], "id": "8b967622-2bc1-4bf0-8ff1-d02826c2249d", "name": "Supabase Vector Store", "credentials": {"supabaseApi": {"id": "OeYUddl4OaIohMCC", "name": "MynotebookLM Supabase"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [580, 320], "id": "e7eae8b9-3647-4e2b-be7e-6c3f2fc80c7b", "name": "Embeddings OpenAI", "credentials": {"openAiApi": {"id": "hNalDChhNUDtYG7T", "name": "OpenAi account"}}}, {"parameters": {"jsonSchemaExample": "{\n\t\"output\": [\n  {\n    \"text\": \"Formula 1 (also known as Formula One) is a premier motorsport championship organized by the FIA...\",\n    \"citations\": [\n      {\n        \"chunk_index\": 0,\n        \"chunk_source_id\": \"701bd18c-2531-48e5-8d09-df22e7cc3d21\",\n        \"chunk_lines_from\": 25,\n        \"chunk_lines_to\": 50\n      }\n    ]\n  },\n  {\n    \"text\": \"Formula 1 (also known as Formula One) is a premier motorsport championship organized by the FIA...\",\n    \"citations\": [\n      {\n        \"chunk_index\": 0,\n        \"chunk_source_id\": \"701bd18c-2531-48e5-8d09-df22e7cc3d21\",\n        \"chunk_lines_from\": 25,\n        \"chunk_lines_to\": 50\n      }\n    ]\n  }\n]\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [800, 20], "id": "5d0dbc4f-50d1-4287-92c8-c917674350c8", "name": "Structured Output Parser"}, {"parameters": {"tableId": "n8n_chat_histories", "fieldsUi": {"fieldValues": [{"fieldId": "session_id", "fieldValue": "={{ $('Webhook').item.json.body.session_id }}"}, {"fieldId": "message", "fieldValue": "{\"type\": \"ai\", \"content\": \"{\\\"output\\\":[{\\\"text\\\":\\\"Sorry, I encountered an error creating a response. Please check the error log.\\\",\\\"citations\\\":[]}]}\", \"tool_calls\": [], \"additional_kwargs\": {}, \"response_metadata\": {}, \"invalid_tool_calls\": []}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [680, -180], "id": "5326f44f-8702-4bfe-8771-c316fcdb3cfd", "name": "Supabase", "alwaysOutputData": true, "credentials": {"supabaseApi": {"id": "OeYUddl4OaIohMCC", "name": "MynotebookLM Supabase"}}}, {"parameters": {"content": "## To Do \n- Configure the Supabase nodes to reference your Project", "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [600, -500], "id": "14b2e53e-f99f-4368-8172-99cd8afa1afb", "name": "<PERSON><PERSON>"}, {"parameters": {"modelName": "models/gemini-2.5-flash-preview-04-17-thinking", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [0, 200], "id": "4a2dc897-d02f-46b8-8c92-71a4af3d486e", "name": "Google Gemini Chat Model", "credentials": {"googlePalmApi": {"id": "PzC8XiX0nzmyH9AA", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"model": {"__rl": true, "value": "claude-3-7-sonnet-********", "mode": "list", "cachedResultName": "<PERSON> 3.7"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatAnthropic", "typeVersion": 1.3, "position": [-160, 200], "id": "fe8982cd-2cc9-407c-a3fd-91bdd3248045", "name": "Anthropic <PERSON>", "credentials": {"anthropicApi": {"id": "LIuOf61utMGpqNxm", "name": "Anthropic account LL"}}}, {"parameters": {"model": {"__rl": true, "value": "o3", "mode": "list", "cachedResultName": "o3"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [160, 200], "id": "1797c365-bc21-4de1-9bf1-7a17ff3c0838", "name": "OpenAI Chat Model1", "credentials": {"openAiApi": {"id": "xh2u9lLOh2CjFrk7", "name": "OpenAi account 4"}}}, {"parameters": {"content": "[![The AI Automators](https://www.theaiautomators.com/wp-content/uploads/2025/03/gray-logo.png)](https://www.theaiautomators.com/)\n## InsightsLM\nhttps://github.com/theaiautomators/insights-lm-public", "height": 220, "width": 280, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-60, -500], "id": "12098b0d-706f-4472-a57f-************", "name": "Sticky Note8"}], "pinData": {}, "connections": {"Webhook": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[], [{"node": "Supabase", "type": "main", "index": 0}]]}, "Postgres Chat Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Supabase Vector Store": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "Supabase Vector Store", "type": "ai_embedding", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "AI Agent", "type": "ai_outputParser", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "18ad3a32-f388-4eaf-9897-b335aea3d3c7", "meta": {"templateCredsSetupCompleted": true, "instanceId": "b5d1ea132a4e071e6288b3143f31284b91560858bdef3f0c88a49f587fc91a29"}, "id": "O7IxHwlMlj7lPFD9", "tags": [{"createdAt": "2025-05-12T13:43:59.783Z", "updatedAt": "2025-05-12T13:43:59.783Z", "id": "d3ygIhrGjDmzgrW0", "name": "TheAIAutomators.com"}]}