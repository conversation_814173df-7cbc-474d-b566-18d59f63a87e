{"name": "InsightsLM - Upsert to Vector Store", "nodes": [{"parameters": {"httpMethod": "POST", "path": "19566c6c-e0a5-4a8f-ba1a-5203c2b663b7", "authentication": "headerAuth", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [320, -20], "id": "e80f97df-d2a3-40e5-8ebe-05e7123482ee", "name": "Webhook", "webhookId": "19566c6c-e0a5-4a8f-ba1a-5203c2b663b7", "credentials": {"httpHeaderAuth": {"id": "39evQ95L86jhtb3I", "name": "MyNoteBookLM Auth"}}}, {"parameters": {"mode": "insert", "tableName": {"__rl": true, "value": "documents", "mode": "list", "cachedResultName": "documents"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1.1, "position": [1812, -20], "id": "8a9a6814-b5db-4f2e-8877-a62633c042d8", "name": "Supabase Vector Store", "retryOnFail": true, "maxTries": 5, "waitBetweenTries": 5000, "alwaysOutputData": true, "credentials": {"supabaseApi": {"id": "OeYUddl4OaIohMCC", "name": "MynotebookLM Supabase"}}}, {"parameters": {"options": {"metadata": {"metadataValues": [{"name": "notebook_id", "value": "={{ $('Code').item.json.notebook_id }}"}, {"name": "source_id", "value": "={{ $('Code').item.json.source_id }}"}]}}}, "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "typeVersion": 1, "position": [1916, 202.5], "id": "bdc647ec-d262-46ae-b96b-47577f237dc4", "name": "Default Data Loader"}, {"parameters": {"chunkOverlap": 200, "options": {}}, "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "typeVersion": 1, "position": [2004, 400], "id": "66adf3a1-e086-4562-b5d3-5e700a49dc14", "name": "Recursive Character Text Splitter"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [1796, 200], "id": "aa1fc9fb-8942-4aa1-9fcc-525595accaf7", "name": "Embeddings OpenAI", "credentials": {"openAiApi": {"id": "hNalDChhNUDtYG7T", "name": "OpenAi account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "dab34521-ffef-41db-9eb3-8985fd108466", "name": "text", "value": "={{ $('Code').item.json.extracted_text }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1576, -20], "id": "e0233d37-8a61-4ad6-b0b8-1209b7da0ec5", "name": "<PERSON>"}, {"parameters": {"jsCode": "// Example: run this in an n8n Code node (JavaScript)\n\nlet notebook_id;\nlet source_id;\n\ntry {\n  const webhookData = $('Webhook').first();\n  if (webhookData && webhookData.json?.body?.file_path) {\n    const path = webhookData.json.body.file_path;\n    notebook_id = path.split('/')[0];\n    source_id = webhookData.json.body.source_id;\n  }\n} catch (e) {\n  // Webhook not executed or doesn't exist in this context\n  notebook_id = $input.first().json.notebook_id;\n  source_id = $input.first().json.source_id;\n}\n\nconst extracted_text = $input.first().json.extracted_text;\n\nreturn [\n  {\n    json: {\n      notebook_id,\n      extracted_text,\n      source_id\n    },\n  },\n];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [760, -20], "id": "89e5c09e-6321-4c66-94f8-d1bdb6cccd99", "name": "Code"}, {"parameters": {"method": "POST", "url": "https://yfvmutoxmibqzvyklggr.supabase.co/functions/v1/process-document-callback", "authentication": "predefinedCredentialType", "nodeCredentialType": "supabaseApi", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"source_id\": \"{{ $('Code').item.json.source_id }}\",\n  \"status\": \"completed\"\n}\n", "options": {"redirect": {"redirect": {}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2420, -20], "id": "01a9b118-2d17-4637-98f7-4fe309c7cc54", "name": "HTTP Request", "credentials": {"supabaseApi": {"id": "OeYUddl4OaIohMCC", "name": "MynotebookLM Supabase"}}}, {"parameters": {"promptType": "define", "text": "={{ $json.extracted_text }}", "hasOutputParser": true, "messages": {"messageValues": [{"message": "=Based on the data provided, output an appropriate title and summary of the document. Only output in JSON in the following format / structure\n\n{\n\t\"title\": \"<ADD>\",\n\t\"summary\": \"<ADD>\"\n}"}]}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.6, "position": [980, -20], "id": "88660990-81f1-4ed0-aaa8-c64c1ba1d2cf", "name": "Generate Title & Description"}, {"parameters": {"jsonSchemaExample": "{\n\t\"title\": \"<ADD>\",\n\t\"summary\": \"<ADD>\"\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [1128, 200], "id": "3a83ea73-73e8-4f74-90ad-b55aadc5c8a3", "name": "Structured Output Parser"}, {"parameters": {"model": {"__rl": true, "value": "gpt-4.1-mini", "mode": "list", "cachedResultName": "gpt-4.1-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [1008, 200], "id": "e16a04be-5702-4d01-92f5-45af5f90b97a", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "hNalDChhNUDtYG7T", "name": "OpenAi account"}}}, {"parameters": {"operation": "update", "tableId": "sources", "filters": {"conditions": [{"keyName": "id", "condition": "eq", "keyValue": "={{ $('Code').item.json.source_id }}"}]}, "fieldsUi": {"fieldValues": [{"fieldId": "content", "fieldValue": "={{ $('Code').item.json.extracted_text }}"}, {"fieldId": "summary", "fieldValue": "={{ $json.output.summary }}"}, {"fieldId": "display_name", "fieldValue": "={{ $json.output.title }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [1356, -20], "id": "0487c4af-d2b5-4208-bbfa-06af8ebb8ee5", "name": "Supabase", "credentials": {"supabaseApi": {"id": "OeYUddl4OaIohMCC", "name": "MynotebookLM Supabase"}}}, {"parameters": {"workflowId": {"__rl": true, "value": "AzZ5a2zCGU1O3MRV", "mode": "list", "cachedResultName": "InsightsLM - Extract Text"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"filePath": "={{ $json.body.file_path }}"}, "matchingColumns": ["filePath"], "schema": [{"id": "filePath", "displayName": "filePath", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [540, -20], "id": "5b4ae6b1-e39f-452f-a46b-9832f7b346bc", "name": "Extract Text"}, {"parameters": {"workflowInputs": {"values": [{"name": "notebook_id"}, {"name": "extracted_text"}, {"name": "source_id"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [320, -260], "id": "fb8ef16d-0ba1-4659-ab05-d3a53fc155cc", "name": "When Executed by Another Workflow"}, {"parameters": {"aggregate": "aggregateAllItemData", "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [2180, -20], "id": "effe2ff2-00bc-4f74-bc50-6078685dcfd5", "name": "Aggregate"}, {"parameters": {"content": "## To Do \n- Configure the Supabase nodes to reference your Project\n- Configure \"Extract Text\" to hit the correct workflow\n- Update the Host of the \"HTTP Request\" to hit your project", "width": 580, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1460, -300], "id": "8c087d38-88b2-4c9f-8267-d8121addea2a", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "[![The AI Automators](https://www.theaiautomators.com/wp-content/uploads/2025/03/gray-logo.png)](https://www.theaiautomators.com/)\n## InsightsLM\nhttps://github.com/theaiautomators/insights-lm-public", "height": 220, "width": 280, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [740, -340], "id": "6d8672e9-000b-4e81-9f2b-a665f2972e52", "name": "Sticky Note8"}], "pinData": {}, "connections": {"Webhook": {"main": [[{"node": "Extract Text", "type": "main", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Supabase Vector Store", "type": "ai_document", "index": 0}]]}, "Recursive Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "Supabase Vector Store", "type": "ai_embedding", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Supabase Vector Store", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "Generate Title & Description", "type": "main", "index": 0}]]}, "Supabase Vector Store": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Generate Title & Description", "type": "ai_outputParser", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Generate Title & Description", "type": "ai_languageModel", "index": 0}]]}, "Generate Title & Description": {"main": [[{"node": "Supabase", "type": "main", "index": 0}]]}, "Supabase": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Extract Text": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "5bb6ec60-c693-47cc-9d5a-a27d71bf284f", "meta": {"templateCredsSetupCompleted": true, "instanceId": "b5d1ea132a4e071e6288b3143f31284b91560858bdef3f0c88a49f587fc91a29"}, "id": "IQcdcedwXg2w3AuW", "tags": [{"createdAt": "2025-05-12T13:43:59.783Z", "updatedAt": "2025-05-12T13:43:59.783Z", "id": "d3ygIhrGjDmzgrW0", "name": "TheAIAutomators.com"}]}