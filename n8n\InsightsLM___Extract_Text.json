{"name": "InsightsLM - Extract Text", "nodes": [{"parameters": {"workflowInputs": {"values": [{"name": "filePath"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [60, 180], "id": "92f1772c-8782-4c8d-9374-18b599a89655", "name": "When Executed by Another Workflow"}, {"parameters": {"method": "POST", "url": "=https://yfvmutoxmibqzvyklggr.supabase.co/storage/v1/object/sign/sources/{{ $json.filePath }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "supabaseApi", "sendBody": true, "specifyBody": "json", "jsonBody": "{\n  \"expiresIn\": 60\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [280, 180], "id": "eacc6b5c-ff5f-40c1-bfb2-4968317a1ecc", "name": "Generate Signed URL", "credentials": {"supabaseApi": {"id": "OeYUddl4OaIohMCC", "name": "MynotebookLM Supabase"}}}, {"parameters": {"url": "=https://yfvmutoxmibqzvyklggr.supabase.co/storage/v1/{{ $json.signedURL }}", "options": {"response": {"response": {"fullResponse": true}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [500, 180], "id": "9c5245a7-cbe8-4217-902b-7f1c42bfc3e2", "name": "Download File"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.headers[\"content-type\"] }}", "rightValue": "application/pdf", "operator": {"type": "string", "operation": "equals"}, "id": "dc0dbe8c-70d5-4127-95f8-0fbd3f1a6bad"}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "2307a9d5-7872-49dc-9aeb-60697b91dc84", "leftValue": "={{ $json.headers[\"content-type\"] }}", "rightValue": "audio/mpeg", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "87532eb7-b821-4d84-9d8e-c17136b0a88a", "leftValue": "={{ $json.headers[\"content-type\"] }}", "rightValue": "text/plain", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [720, 180], "id": "b9643850-737a-405f-9ff0-1903d9424cd1", "name": "Switch"}, {"parameters": {"operation": "pdf", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [940, -20], "id": "ceeb5000-0dbc-4d18-9a29-7bc2c83ff959", "name": "Extract from File"}, {"parameters": {"resource": "audio", "operation": "transcribe", "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [940, 180], "id": "e8c24cd1-0d5e-452e-97ef-cb997a9c603e", "name": "OpenAI", "credentials": {"openAiApi": {"id": "hNalDChhNUDtYG7T", "name": "OpenAi account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "1f5bee5d-939c-4df3-8e7d-0696ac3aa702", "name": "text", "value": "={{ $('Download File').item.json.data }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [940, 380], "id": "45462a2a-a13e-45c0-8857-21f924c721ea", "name": "Edit Fields1"}, {"parameters": {"assignments": {"assignments": [{"id": "9fea51c0-b3a9-49b5-8608-3264c9d00304", "name": "extracted_text", "value": "={{ $json.text }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1160, 180], "id": "83cae3e9-ba19-4cdd-ac7e-0d4b4515eaad", "name": "Extracted Text"}, {"parameters": {"content": "## To Do \n- Configure the Supabase nodes to reference your Project\n- Update the Host of the \"HTTP Requests\" to hit your project\n- Setup OpenAI credentials", "width": 680, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1420, 100], "id": "a550ac25-8226-46af-8c55-5f42f1abd0e9", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "[![The AI Automators](https://www.theaiautomators.com/wp-content/uploads/2025/03/gray-logo.png)](https://www.theaiautomators.com/)\n## InsightsLM\nhttps://github.com/theaiautomators/insights-lm-public", "height": 220, "width": 280, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [60, -200], "id": "3f1e49b6-b7b4-49d0-a2e7-c50ab1cd8f3d", "name": "Sticky Note8"}], "pinData": {}, "connections": {"Generate Signed URL": {"main": [[{"node": "Download File", "type": "main", "index": 0}]]}, "Download File": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "Extract from File", "type": "main", "index": 0}], [{"node": "OpenAI", "type": "main", "index": 0}], [{"node": "Edit Fields1", "type": "main", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "Generate Signed URL", "type": "main", "index": 0}]]}, "Extract from File": {"main": [[{"node": "Extracted Text", "type": "main", "index": 0}]]}, "OpenAI": {"main": [[{"node": "Extracted Text", "type": "main", "index": 0}]]}, "Edit Fields1": {"main": [[{"node": "Extracted Text", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "28c069b3-6e86-4169-82b3-50bcfad834f7", "meta": {"instanceId": "b5d1ea132a4e071e6288b3143f31284b91560858bdef3f0c88a49f587fc91a29"}, "id": "AzZ5a2zCGU1O3MRV", "tags": []}