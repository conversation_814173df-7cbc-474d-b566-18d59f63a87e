{"name": "InsightsLM - Process Additional Sources", "nodes": [{"parameters": {"httpMethod": "POST", "path": "670882ea-5c1e-4b50-9f41-4792256af985", "authentication": "headerAuth", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-20, -100], "id": "e84393f1-d235-49b6-ae80-14fab4c2eaf3", "name": "Webhook", "webhookId": "670882ea-5c1e-4b50-9f41-4792256af985", "credentials": {"httpHeaderAuth": {"id": "39evQ95L86jhtb3I", "name": "MyNoteBookLM Auth"}}}, {"parameters": {"workflowId": {"__rl": true, "value": "IQcdcedwXg2w3AuW", "mode": "list", "cachedResultName": "InsightsLM - Upsert to Vector Store"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"notebook_id": "={{ $('Webhook').item.json.body.notebookId }}", "extracted_text": "={{ $('Webhook').item.json.body.content }}", "source_id": "={{ $('Webhook').item.json.body.sourceId }}"}, "matchingColumns": [], "schema": [{"id": "notebook_id", "displayName": "notebook_id", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "extracted_text", "displayName": "extracted_text", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "source_id", "displayName": "source_id", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [880, -280], "id": "21f00127-d042-444c-9d5b-a5827ff303b7", "name": "Execute Workflow"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.body.type }}", "rightValue": "copied-text", "operator": {"type": "string", "operation": "equals"}, "id": "5057cf19-e998-4cbb-b3a2-95bb8b2a7355"}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "9b0e22e8-984f-46ba-b83e-52d3feda3985", "leftValue": "={{ $json.body.type }}", "rightValue": "multiple-websites", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [220, -105], "id": "ea64f96b-552e-4036-a467-9e721a9348b3", "name": "Switch"}, {"parameters": {"fieldToSplitOut": "body.urls", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [440, -5], "id": "df3e6c68-9618-4600-82b7-547815e30e0d", "name": "Split Out"}, {"parameters": {"options": {"reset": false}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [660, -5], "id": "5c238b17-d28f-42a7-a273-dd5ce9cf0f6c", "name": "Loop Over Items"}, {"parameters": {"url": "=https://r.jina.ai/{{ $json['body.urls'] }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Accept", "value": "application/json"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [980, 80], "id": "ab711941-fffb-48a9-8f12-0417503bacf3", "name": "Fetch Webpage with Jina.ai"}, {"parameters": {"workflowId": {"__rl": true, "value": "IQcdcedwXg2w3AuW", "mode": "list", "cachedResultName": "InsightsLM - Upsert to Vector Store"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"notebook_id": "={{ $('Switch').item.json.body.notebookId }}", "extracted_text": "={{ $json.content }}", "source_id": "={{ $('Switch').item.json.body.sourceIds[$('Loop Over Items').item.pairedItem.item] }}"}, "matchingColumns": [], "schema": [{"id": "notebook_id", "displayName": "notebook_id", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "extracted_text", "displayName": "extracted_text", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "source_id", "displayName": "source_id", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [1640, 80], "id": "f64e5ee1-1ebe-48f1-b77e-86ed6943eb79", "name": "Execute Workflow1"}, {"parameters": {"operation": "update", "tableId": "sources", "filters": {"conditions": [{"keyName": "id", "condition": "eq", "keyValue": "={{ $('Switch').item.json.body.sourceIds[$('Loop Over Items').item.pairedItem.item] }}"}]}, "fieldsUi": {"fieldValues": [{"fieldId": "content", "fieldValue": "={{ $('Fetch Webpage with Jina.ai').item.json.data.content }}"}, {"fieldId": "title", "fieldValue": "={{ $('Fetch Webpage with Jina.ai').item.json.data.title }}"}, {"fieldId": "file_path", "fieldValue": "={{ $('Switch').item.json.body.notebookId }}/{{ $('Switch').item.json.body.sourceIds[$('Loop Over Items').item.pairedItem.item] }}.txt"}, {"fieldId": "file_size", "fieldValue": "9999"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [1420, 80], "id": "629973a6-c406-4963-860b-2a6d90815afa", "name": "Supabase", "credentials": {"supabaseApi": {"id": "OeYUddl4OaIohMCC", "name": "MynotebookLM Supabase"}}}, {"parameters": {"aggregate": "aggregateAllItemData", "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [1860, 140], "id": "66f0f255-3e83-4014-8d50-4d539b8658af", "name": "Aggregate"}, {"parameters": {"method": "POST", "url": "=https://yfvmutoxmibqzvyklggr.supabase.co/storage/v1/object/sources/{{ $('Switch').item.json.body.notebookId }}/{{ $('Switch').item.json.body.sourceIds[$('Loop Over Items').item.pairedItem.item] }}.txt", "authentication": "predefinedCredentialType", "nodeCredentialType": "supabaseApi", "sendBody": true, "contentType": "raw", "rawContentType": "text/plain", "body": "={{ $json.data.url }}\n{{ $json.data.content }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1200, 80], "id": "932f87b7-3975-4304-9225-7e5dd709216b", "name": "Upload File to Bucket", "credentials": {"supabaseApi": {"id": "OeYUddl4OaIohMCC", "name": "MynotebookLM Supabase"}}}, {"parameters": {"method": "POST", "url": "=https://yfvmutoxmibqzvyklggr.supabase.co/storage/v1/object/sources/{{ $('Switch').item.json.body.notebookId }}/{{ $json.body.sourceId }}.txt", "authentication": "predefinedCredentialType", "nodeCredentialType": "supabaseApi", "sendBody": true, "contentType": "raw", "rawContentType": "text/plain", "body": "={{ $json.data.url }}\n{{ $json.data.content }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [440, -280], "id": "0c971148-8f1e-4da7-aee8-f6a9a4f7d58f", "name": "Upload File to Bucket1", "credentials": {"supabaseApi": {"id": "OeYUddl4OaIohMCC", "name": "MynotebookLM Supabase"}}}, {"parameters": {"operation": "update", "tableId": "sources", "filters": {"conditions": [{"keyName": "id", "condition": "eq", "keyValue": "={{ $('Webhook').item.json.body.sourceId }}"}]}, "fieldsUi": {"fieldValues": [{"fieldId": "file_path", "fieldValue": "={{ $('Switch').item.json.body.notebookId }}/{{ $('Webhook').item.json.body.sourceId }}.txt"}, {"fieldId": "file_size", "fieldValue": "9999"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [660, -280], "id": "8b5103e1-46bd-4959-8b1a-0e288759723f", "name": "Supabase1", "credentials": {"supabaseApi": {"id": "OeYUddl4OaIohMCC", "name": "MynotebookLM Supabase"}}}, {"parameters": {"content": "## To Do \n- Configure the Supabase nodes to reference your Project\n- Configure \"Extract Workflow\" to hit the \"Upsert to Vector Store\" workflow\n- Update the Host of the \"HTTP Requests\" to hit your project", "width": 680, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [480, -600], "id": "e0772715-ac1a-4ceb-8017-b07e568c2251", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "[![The AI Automators](https://www.theaiautomators.com/wp-content/uploads/2025/03/gray-logo.png)](https://www.theaiautomators.com/)\n## InsightsLM\nhttps://github.com/theaiautomators/insights-lm-public", "height": 220, "width": 280, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [0, -600], "id": "3cb7b6af-1480-47dc-aa7b-07be3b2eb433", "name": "Sticky Note8"}], "pinData": {}, "connections": {"Webhook": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "Upload File to Bucket1", "type": "main", "index": 0}], [{"node": "Split Out", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "Fetch Webpage with Jina.ai", "type": "main", "index": 0}]]}, "Fetch Webpage with Jina.ai": {"main": [[{"node": "Upload File to Bucket", "type": "main", "index": 0}]]}, "Supabase": {"main": [[{"node": "Execute Workflow1", "type": "main", "index": 0}]]}, "Execute Workflow1": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Upload File to Bucket": {"main": [[{"node": "Supabase", "type": "main", "index": 0}]]}, "Upload File to Bucket1": {"main": [[{"node": "Supabase1", "type": "main", "index": 0}]]}, "Supabase1": {"main": [[{"node": "Execute Workflow", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "543da2bf-a142-4b01-bffc-81c36407da2f", "meta": {"templateCredsSetupCompleted": true, "instanceId": "b5d1ea132a4e071e6288b3143f31284b91560858bdef3f0c88a49f587fc91a29"}, "id": "3FuYqT7cPNPitvak", "tags": [{"createdAt": "2025-05-12T13:43:59.783Z", "updatedAt": "2025-05-12T13:43:59.783Z", "id": "d3ygIhrGjDmzgrW0", "name": "TheAIAutomators.com"}]}