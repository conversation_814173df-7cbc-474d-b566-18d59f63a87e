{"name": "InsightsLM - Generate Notebook Details", "nodes": [{"parameters": {"httpMethod": "POST", "path": "0c488f50-8d6a-48a0-b056-5f7cfca9efe2", "authentication": "headerAuth", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [140, -200], "id": "010f9da2-e94a-4eeb-8abc-7a11b5df02d3", "name": "Webhook", "webhookId": "0c488f50-8d6a-48a0-b056-5f7cfca9efe2", "credentials": {"httpHeaderAuth": {"id": "39evQ95L86jhtb3I", "name": "MyNoteBookLM Auth"}}}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.2, "position": [1616, -200], "id": "d617ebc0-0500-403b-babc-205733ce36a4", "name": "Respond to Webhook"}, {"parameters": {"promptType": "define", "text": "={{ $json.extracted_text }}", "hasOutputParser": true, "messages": {"messageValues": [{"message": "=Based on the data provided, output an appropriate title and summary of the document. \n\nAlso output an appropriate UTF-8 emoji for the notebook. - example: 🏆\nAnd output an appropriate color from this list\n\nslate\ngray\nzinc\nneutral\nstone\nred\norange\namber\nyellow\nlime\ngreen\nemerald\nteal\ncyan\nsky\nblue\nindigo\nviolet\npurple\nfuchsia\npink\nrose\n\nAlso output a list of 5 Example Questions that could be asked of this document. For example \"How are the rules and regulations of tennis enforced?\" - Maximum 10 words each\n\nOnly output in JSON."}]}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.6, "position": [1240, -200], "id": "66e19df1-eae3-43f0-93d1-56def58c1432", "name": "Generate Title & Description"}, {"parameters": {"jsonSchemaExample": "{\n\t\"title\": \"<ADD>\",\n\t\"summary\": \"<ADD>\",\n  \"notebook_icon\": \"<ADD>\",\n  \"background_color\": \"<ADD>\",\n  \"example_questions\": [\"ADD\",\"ADD\"]\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [1388, 20], "id": "5800e2a7-6e7f-4227-a124-43d05b2a8542", "name": "Structured Output Parser"}, {"parameters": {"model": {"__rl": true, "value": "gpt-4.1-mini", "mode": "list", "cachedResultName": "gpt-4.1-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [1268, 20], "id": "fb70591a-e3fb-4430-8902-70da6a397200", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "hNalDChhNUDtYG7T", "name": "OpenAi account"}}}, {"parameters": {"workflowId": {"__rl": true, "value": "AzZ5a2zCGU1O3MRV", "mode": "list", "cachedResultName": "InsightsLM - Extract Text"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"filePath": "={{ $json.body.filePath }}"}, "matchingColumns": ["filePath"], "schema": [{"id": "filePath", "displayName": "filePath", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [1020, 0], "id": "df36e109-47a6-4f1b-a775-740b4b3ceb7e", "name": "Extract Text"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "a25d08b6-2c99-4e08-af5f-62b13e6c4fdb", "leftValue": "={{ $json.body.sourceType }}", "rightValue": "=text", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}, {"id": "3b5d5dce-76c4-445a-bab7-53c2114d52be", "leftValue": "={{ $json.body.sourceType }}", "rightValue": "website", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "or"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [360, -200], "id": "fdb03c27-aa2b-4ce4-a627-100599928646", "name": "If"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "ea61516d-9144-4b4b-904e-cf280d5bd08d", "leftValue": "={{ $json.body.sourceType }}", "rightValue": "text", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [580, -300], "id": "3fe02905-4fd7-45bc-bdec-72de6a6d8a97", "name": "If1"}, {"parameters": {"assignments": {"assignments": [{"id": "c5924d7e-6185-49a5-b61c-d244a40bd9b2", "name": "=extracted_text", "value": "={{ $json.body.content }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1020, -400], "id": "6ca2f532-9da2-46cf-a88e-edc2264fd3a7", "name": "Set Text"}, {"parameters": {"url": "=https://r.jina.ai/{{ $json.body.filePath }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Accept", "value": "application/json"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [800, -200], "id": "7f96ba8f-875b-4368-9022-94c06acd9936", "name": "Fetch Webpage with Jina.ai"}, {"parameters": {"assignments": {"assignments": [{"id": "c5924d7e-6185-49a5-b61c-d244a40bd9b2", "name": "=extracted_text", "value": "={{ $json.data.content }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1020, -200], "id": "0e106e48-8a5c-4142-8906-e2b5291d11e2", "name": "Set Text1"}, {"parameters": {"content": "## To Do \n- Configure the Extract Text node to trigger the \"Extract Text\" workflow", "width": 680, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1360, -440], "id": "2011755c-70ad-4bd7-811b-e6bface53763", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "[![The AI Automators](https://www.theaiautomators.com/wp-content/uploads/2025/03/gray-logo.png)](https://www.theaiautomators.com/)\n## InsightsLM\nhttps://github.com/theaiautomators/insights-lm-public", "height": 220, "width": 280, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [140, -580], "id": "b0e56210-db45-4b10-b513-ddbac2e7d222", "name": "Sticky Note8"}], "pinData": {}, "connections": {"Webhook": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Generate Title & Description": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Generate Title & Description", "type": "ai_outputParser", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Generate Title & Description", "type": "ai_languageModel", "index": 0}]]}, "Extract Text": {"main": [[{"node": "Generate Title & Description", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "If1", "type": "main", "index": 0}], [{"node": "Extract Text", "type": "main", "index": 0}]]}, "If1": {"main": [[{"node": "Set Text", "type": "main", "index": 0}], [{"node": "Fetch Webpage with Jina.ai", "type": "main", "index": 0}]]}, "Set Text": {"main": [[{"node": "Generate Title & Description", "type": "main", "index": 0}]]}, "Fetch Webpage with Jina.ai": {"main": [[{"node": "Set Text1", "type": "main", "index": 0}]]}, "Set Text1": {"main": [[{"node": "Generate Title & Description", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "dd220d9f-8dc3-44bc-889d-726cde238df3", "meta": {"templateCredsSetupCompleted": true, "instanceId": "b5d1ea132a4e071e6288b3143f31284b91560858bdef3f0c88a49f587fc91a29"}, "id": "5I6KohfDxYQ2xvQ7", "tags": [{"createdAt": "2025-05-12T13:43:59.783Z", "updatedAt": "2025-05-12T13:43:59.783Z", "id": "d3ygIhrGjDmzgrW0", "name": "TheAIAutomators.com"}]}